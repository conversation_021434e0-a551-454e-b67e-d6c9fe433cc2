<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>About - skygamedrop | Professional QR Code Generator</title>
    <meta name="description" content="Learn about skygamedrop - the most comprehensive and user-friendly QR code generator. Discover our mission, features, and commitment to privacy.">
    <meta name="keywords" content="about skygamedrop, QR code generator, about us, privacy-focused, free QR codes">
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 主要图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer">
    <!-- 备用图标库 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.5.1/css/all.min.css" crossorigin="anonymous">
    <!-- 第三备用图标库 -->
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v6.5.1/css/all.css" crossorigin="anonymous">
    <!-- 图标兼容性修复 -->
    <style>
        /* 确保图标正常显示的兼容性修复 */
        .fas, .fa-solid {
            font-family: "Font Awesome 6 Free";
            font-weight: 900;
        }
        .far, .fa-regular {
            font-family: "Font Awesome 6 Free";
            font-weight: 400;
        }
        .fab, .fa-brands {
            font-family: "Font Awesome 6 Brands";
            font-weight: 400;
        }
    </style>
    <!-- 引入更细致的字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        * {
            box-sizing: border-box;
        }
        
        html {
            scroll-behavior: smooth;
            overflow-x: hidden;
            width: 100%;
        }
        
        body {
            max-width: 100vw;
            overflow-x: hidden;
            width: 100%;
            margin: 0;
            padding: 0;
            position: relative;
            font-family: 'Inter', 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            font-weight: 300;
            letter-spacing: -0.01em;
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        /* 防止所有容器超出视口宽度 */
        .container {
            max-width: 100%;
            width: 100%;
            margin: 0 auto;
            padding-left: 1rem;
            padding-right: 1rem;
        }

        /* 细致的字体样式设置 */
        h1, h2, h3, h4, h5, h6 {
            font-family: 'Poppins', 'Inter', sans-serif;
            font-weight: 300;
            letter-spacing: -0.02em;
            line-height: 1.2;
            color: #1f2937;
        }

        h1 {
            font-weight: 200;
            letter-spacing: -0.03em;
        }

        h2 {
            font-weight: 300;
            letter-spacing: -0.025em;
        }

        h3 {
            font-weight: 400;
            letter-spacing: -0.02em;
        }

        p {
            font-family: 'Inter', sans-serif;
            font-weight: 300;
            letter-spacing: -0.01em;
            line-height: 1.7;
            color: #4b5563;
        }

        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .hero-title {
            font-family: 'Poppins', sans-serif;
            font-weight: 100;
            letter-spacing: -0.04em;
            line-height: 1.1;
        }

        .feature-title {
            font-family: 'Poppins', sans-serif;
            font-weight: 400;
            letter-spacing: -0.015em;
        }

        .card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            margin-bottom: 2rem;
        }

        /* 导航和菜单的字体 */
        nav a {
            font-family: 'Inter', sans-serif;
            font-weight: 300;
            letter-spacing: -0.01em;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .container {
                padding-left: 0.75rem;
                padding-right: 0.75rem;
                max-width: 100%;
                width: 100%;
            }
            
            .card {
                padding: 1rem;
                margin-bottom: 1rem;
                width: 100%;
                max-width: 100%;
            }

            .text-4xl {
                font-size: 2rem;
            }

            .text-5xl {
                font-size: 2.5rem;
            }
            
            .md\\:text-6xl {
                font-size: 2.5rem;
            }
            
            .md\\:text-4xl {
                font-size: 2rem;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding-left: 0.5rem;
                padding-right: 0.5rem;
                max-width: 100%;
                width: 100%;
            }

            .card {
                padding: 0.75rem;
                margin-bottom: 0.75rem;
                width: 100%;
                max-width: 100%;
            }

            .text-xl {
                font-size: 1.125rem;
            }
            
            .md\\:text-2xl {
                font-size: 1.25rem;
            }
        }

        /* 防止移动端水平滚动的额外措施 */
        @media (max-width: 768px) {
            html, body {
                touch-action: pan-y;
                -webkit-overflow-scrolling: touch;
            }
            
            header, section, footer {
                max-width: 100vw;
                overflow-x: hidden;
            }
            
            img {
                max-width: 100%;
                height: auto;
                object-fit: contain;
            }
        }
    </style>
</head>

<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-lg sticky top-0 z-50">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <img src="./images/logo.png" alt="skygamedrop" class="h-10 w-auto">
                </div>
                <nav class="hidden md:flex space-x-6">
                    <a href="index.html#home" class="text-gray-600 hover:text-purple-600 transition-colors font-medium">Home</a>
                    <a href="about.html#about" class="text-purple-600 font-medium">About</a>
                </nav>
                <!-- Mobile menu button -->
                <button class="md:hidden text-gray-600 hover:text-purple-600" id="mobile-menu-btn">
                    <i class="fas fa-bars text-xl"></i>
                </button>
            </div>
            <!-- Mobile menu -->
            <div class="md:hidden mt-4 hidden" id="mobile-menu">
                <nav class="flex flex-col space-y-2">
                    <a href="index.html#home" class="text-gray-600 hover:text-purple-600 transition-colors py-2">Home</a>
                    <a href="about.html#about" class="text-purple-600 py-2">About</a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section id="about" class="gradient-bg py-16">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-4xl md:text-6xl hero-title text-white mb-6">
                <i class="fas fa-info-circle mr-4"></i>About skygamedrop
            </h1>
            <p class="text-xl md:text-2xl text-white opacity-90 mb-8 max-w-3xl mx-auto">
                Discover the story behind the most comprehensive and user-friendly QR code generator on the web
            </p>
        </div>
    </section>

    <!-- Mission Section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl feature-title text-gray-800 mb-6">Our Mission</h2>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                        We believe that creating professional QR codes should be simple, free, and accessible to everyone. 
                        Our mission is to provide the most powerful yet user-friendly QR code generation tools without compromising on privacy or quality.
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
                    <div class="card">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                                <i class="fas fa-heart text-blue-600 text-xl"></i>
                            </div>
                            <h3 class="text-xl feature-title">Built with Passion</h3>
                        </div>
                        <p class="text-gray-600">
                            Every feature in skygamedrop is crafted with care and attention to detail. 
                            We're passionate about creating tools that make your life easier and your work more professional.
                        </p>
                    </div>

                    <div class="card">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4">
                                <i class="fas fa-users text-green-600 text-xl"></i>
                            </div>
                            <h3 class="text-xl feature-title">Community Driven</h3>
                        </div>
                        <p class="text-gray-600">
                            Our development is guided by user feedback and real-world needs. 
                            We listen to our community and continuously improve based on your suggestions and requirements.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Deep Dive -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="max-w-6xl mx-auto">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl feature-title text-gray-800 mb-6">What Makes Us Different</h2>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                        We've reimagined what a QR code generator should be, focusing on the features that matter most to our users.
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Privacy First -->
                    <div class="card text-center">
                        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-shield-halved text-2xl text-red-600"></i>
                        </div>
                        <h3 class="text-xl feature-title mb-4">Privacy First</h3>
                        <p class="text-gray-600 mb-4">
                            All QR code generation happens locally in your browser. Your data never leaves your device, ensuring complete privacy and security.
                        </p>
                        <ul class="text-sm text-gray-500 text-left space-y-1">
                            <li>• No server uploads</li>
                            <li>• No data tracking</li>
                            <li>• No registration required</li>
                        </ul>
                    </div>

                    <!-- Professional Quality -->
                    <div class="card text-center">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-gem text-2xl text-blue-600"></i>
                        </div>
                        <h3 class="text-xl feature-title mb-4">Professional Quality</h3>
                        <p class="text-gray-600 mb-4">
                            Generate high-resolution QR codes suitable for both digital and print use, with multiple format options and customization features.
                        </p>
                        <ul class="text-sm text-gray-500 text-left space-y-1">
                            <li>• Up to 2048px resolution</li>
                            <li>• PNG & SVG formats</li>
                            <li>• Print-ready quality</li>
                        </ul>
                    </div>

                    <!-- Advanced Customization -->
                    <div class="card text-center">
                        <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-palette text-2xl text-purple-600"></i>
                        </div>
                        <h3 class="text-xl feature-title mb-4">Advanced Customization</h3>
                        <p class="text-gray-600 mb-4">
                            Extensive styling options including colors, patterns, logos, and gradients to match your brand perfectly.
                        </p>
                        <ul class="text-sm text-gray-500 text-left space-y-1">
                            <li>• Custom colors & gradients</li>
                            <li>• Logo integration</li>
                            <li>• Multiple dot styles</li>
                        </ul>
                    </div>

                    <!-- Multiple Content Types -->
                    <div class="card text-center">
                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-layer-group text-2xl text-green-600"></i>
                        </div>
                        <h3 class="text-xl feature-title mb-4">Multiple Content Types</h3>
                        <p class="text-gray-600 mb-4">
                            Support for various content types including text, URLs, phone numbers, and email addresses with optimized formatting.
                        </p>
                        <ul class="text-sm text-gray-500 text-left space-y-1">
                            <li>• Text & URLs</li>
                            <li>• Phone numbers</li>
                            <li>• Email addresses</li>
                        </ul>
                    </div>

                    <!-- Real-time Preview -->
                    <div class="card text-center">
                        <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-eye text-2xl text-yellow-600"></i>
                        </div>
                        <h3 class="text-xl feature-title mb-4">Real-time Preview</h3>
                        <p class="text-gray-600 mb-4">
                            See your QR code update instantly as you make changes, with detailed information about size and error correction.
                        </p>
                        <ul class="text-sm text-gray-500 text-left space-y-1">
                            <li>• Instant updates</li>
                            <li>• QR code analytics</li>
                            <li>• Error correction info</li>
                        </ul>
                    </div>

                    <!-- Mobile Optimized -->
                    <div class="card text-center">
                        <div class="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-mobile-screen text-2xl text-indigo-600"></i>
                        </div>
                        <h3 class="text-xl feature-title mb-4">Mobile Optimized</h3>
                        <p class="text-gray-600 mb-4">
                            Fully responsive design that works perfectly on all devices, from desktop computers to smartphones.
                        </p>
                        <ul class="text-sm text-gray-500 text-left space-y-1">
                            <li>• Touch-friendly interface</li>
                            <li>• Responsive design</li>
                            <li>• Cross-platform support</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Technology Stack -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto text-center">
                <h2 class="text-3xl md:text-4xl feature-title text-gray-800 mb-6">Built with Modern Technology</h2>
                <p class="text-xl text-gray-600 mb-12">
                    We use cutting-edge web technologies to ensure the best performance, security, and user experience.
                </p>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fab fa-js-square text-2xl text-orange-600"></i>
                        </div>
                        <h3 class="text-lg feature-title mb-2">Modern JavaScript</h3>
                        <p class="text-gray-600 text-sm">ES6+ features for optimal performance</p>
                    </div>

                    <div class="text-center">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fab fa-css3-alt text-2xl text-blue-600"></i>
                        </div>
                        <h3 class="text-lg feature-title mb-2">Advanced CSS</h3>
                        <p class="text-gray-600 text-sm">Responsive design with modern layouts</p>
                    </div>

                    <div class="text-center">
                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-qrcode text-2xl text-green-600"></i>
                        </div>
                        <h3 class="text-lg feature-title mb-2">QR Code Styling</h3>
                        <p class="text-gray-600 text-sm">Advanced QR code generation library</p>
                    </div>

                    <div class="text-center">
                        <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-rocket text-2xl text-purple-600"></i>
                        </div>
                        <h3 class="text-lg feature-title mb-2">Optimized Performance</h3>
                        <p class="text-gray-600 text-sm">Fast loading and smooth interactions</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Values Section -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl feature-title text-gray-800 mb-6">Our Core Values</h2>
                    <p class="text-xl text-gray-600">
                        These principles guide everything we do and every decision we make.
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div class="text-center">
                        <div class="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                            <i class="fas fa-user-shield text-3xl text-blue-600"></i>
                        </div>
                        <h3 class="text-xl feature-title mb-4">Privacy & Security</h3>
                        <p class="text-gray-600">
                            We believe your data belongs to you. That's why we process everything locally and never store or track your information.
                        </p>
                    </div>

                    <div class="text-center">
                        <div class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                            <i class="fas fa-hand-holding-heart text-3xl text-green-600"></i>
                        </div>
                        <h3 class="text-xl feature-title mb-4">Accessibility</h3>
                        <p class="text-gray-600">
                            Professional QR code generation should be free and accessible to everyone, regardless of technical expertise or budget.
                        </p>
                    </div>

                    <div class="text-center">
                        <div class="w-20 h-20 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                            <i class="fas fa-lightbulb text-3xl text-purple-600"></i>
                        </div>
                        <h3 class="text-xl feature-title mb-4">Innovation</h3>
                        <p class="text-gray-600">
                            We continuously innovate and improve, staying ahead of the curve to provide the best possible user experience.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Future Plans -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto text-center">
                <h2 class="text-3xl md:text-4xl feature-title text-gray-800 mb-6">What's Next</h2>
                <p class="text-xl text-gray-600 mb-12">
                    We're constantly working to improve skygamedrop and add new features based on user feedback.
                </p>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div class="card text-left">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                                <i class="fas fa-plus text-blue-600 text-xl"></i>
                            </div>
                            <h3 class="text-xl feature-title">More Content Types</h3>
                        </div>
                        <p class="text-gray-600">
                            We're working on adding support for WiFi credentials, vCards, calendar events, and more specialized QR code types.
                        </p>
                    </div>

                    <div class="card text-left">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4">
                                <i class="fas fa-chart-line text-green-600 text-xl"></i>
                            </div>
                            <h3 class="text-xl feature-title">Analytics Integration</h3>
                        </div>
                        <p class="text-gray-600">
                            Optional analytics features to help you track QR code performance while maintaining our privacy-first approach.
                        </p>
                    </div>

                    <div class="card text-left">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mr-4">
                                <i class="fas fa-magic text-purple-600 text-xl"></i>
                            </div>
                            <h3 class="text-xl feature-title">AI-Powered Features</h3>
                        </div>
                        <p class="text-gray-600">
                            Smart suggestions for QR code styling and automatic optimization based on content type and intended use.
                        </p>
                    </div>

                    <div class="card text-left">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mr-4">
                                <i class="fas fa-code text-orange-600 text-xl"></i>
                            </div>
                            <h3 class="text-xl feature-title">API Access</h3>
                        </div>
                        <p class="text-gray-600">
                            Developer-friendly API for integrating our QR code generation capabilities into your own applications.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="max-w-2xl mx-auto text-center">
                <h2 class="text-3xl md:text-4xl feature-title text-gray-800 mb-6">Get in Touch</h2>
                <p class="text-xl text-gray-600 mb-8">
                    Have questions, suggestions, or feedback? We'd love to hear from you!
                </p>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div class="card text-center">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-envelope text-2xl text-blue-600"></i>
                        </div>
                        <h3 class="text-lg feature-title mb-2">Email Us</h3>
                        <p class="text-gray-600 mb-4">For general inquiries and support</p>
                        <a href="mailto:<EMAIL>" class="text-blue-600 hover:text-blue-800 transition-colors">
                            <EMAIL>
                        </a>
                    </div>

                    <div class="card text-center">
                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-comments text-2xl text-green-600"></i>
                        </div>
                        <h3 class="text-lg feature-title mb-2">Feedback</h3>
                        <p class="text-gray-600 mb-4">Help us improve with your suggestions</p>
                        <a href="mailto:<EMAIL>" class="text-green-600 hover:text-green-800 transition-colors">
                            <EMAIL>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-12">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center space-x-4 mb-4">
                        <img src="./images/logo.png" alt="skygamedrop" class="h-8 w-auto">
                    </div>
                    <p class="text-gray-300 mb-4">
                        The most comprehensive and user-friendly QR code generator. Create professional,
                        customizable QR codes for any purpose - completely free and privacy-focused.
                    </p>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="index.html#home" class="text-gray-300 hover:text-white transition-colors">Home</a></li>
                        <li><a href="about.html#about" class="text-gray-300 hover:text-white transition-colors">About</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-4">Legal</h3>
                    <ul class="space-y-2">
                        <li><a href="Privacy.html" class="text-gray-300 hover:text-white transition-colors">Privacy Policy</a></li>
                        <li><a href="Terms.html" class="text-gray-300 hover:text-white transition-colors">Terms of Use</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-700 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center text-white">
                <div class="mb-4 md:mb-0">
                    <p class="text-white">&copy; 2025 skygamedrop.com All rights reserved.</p>
                </div>
                <div class="text-sm text-gray-300">
                    <p class="text-gray-300">Built with ❤️ using modern web technologies</p>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-btn').addEventListener('click', function () {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                    // Close mobile menu if open
                    document.getElementById('mobile-menu').classList.add('hidden');
                }
            });
        });

        // 防止移动端水平滚动的额外措施
        function preventHorizontalScroll() {
            // 检查是否有元素超出视口宽度
            const checkOverflow = () => {
                const body = document.body;
                const html = document.documentElement;

                // 如果页面宽度超出视口，强制重置
                if (body.scrollWidth > window.innerWidth || html.scrollWidth > window.innerWidth) {
                    // 查找可能导致溢出的元素
                    const allElements = document.querySelectorAll('*');
                    allElements.forEach(el => {
                        const rect = el.getBoundingClientRect();
                        if (rect.right > window.innerWidth) {
                            // 对超出的元素应用修复
                            el.style.maxWidth = '100%';
                            el.style.overflowX = 'hidden';
                            el.style.wordWrap = 'break-word';
                        }
                    });
                }
            };

            // 页面加载时检查
            checkOverflow();

            // 窗口大小改变时检查
            window.addEventListener('resize', checkOverflow);
        }

        // 在DOM加载完成后执行
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', preventHorizontalScroll);
        } else {
            preventHorizontalScroll();
        }
    </script>
</body>

</html>
