# skygamedrop

A comprehensive and user-friendly online QR code generation tool with extensive customization options.

## 🌟 Features

### 📝 Content Support
- **Text Content** - Support for any text content
- **Website URLs** - Automatic recognition and formatting of URLs  
- **Phone Numbers** - Generate callable phone QR codes
- **Email Addresses** - Generate email link QR codes

### 🎨 Style Customization
- **Error Correction Levels** - L/M/Q/H four levels available
- **Size Adjustment** - Freely adjustable from 200px to 800px
- **Color Configuration** - Custom foreground and background colors
- **Dot Styles** - Square, rounded, dots, classy, and classy-rounded
- **Corner Styles** - Square, rounded, and dot styles
- **Gradient Effects** - Linear gradients with multiple directions
- **Margin Settings** - 0-50px margin adjustment

### 🖼️ Logo Integration
- **File Upload** - Support for PNG, JPG, SVG formats
- **Drag & Drop** - Convenient drag-and-drop upload experience
- **Default Icons** - 8 built-in icons available
- **Size Adjustment** - Adjustable logo size and margin
- **Real-time Preview** - Instant logo effect preview

### 📥 Download Features
- **PNG Format** - Perfect for printing and sharing
- **SVG Format** - Vector format with lossless scaling
- **Custom Sizes** - Download sizes from 512px to 2048px
- **Batch Download** - Multi-size packs and dual-format packs
- **High Quality Output** - Guaranteed download quality

### 📱 User Experience
- **Real-time Preview** - Instant generation with input
- **Responsive Design** - Perfect mobile adaptation
- **Status Indicators** - Clear generation status feedback
- **Information Display** - Show detailed QR code information
- **Tooltips** - Helpful feature explanations
- **Keyboard Shortcuts** - Ctrl+S for quick download

## 🚀 Quick Start

### Online Usage
1. Open `index.html` file
2. Select content type (Text/URL/Phone/Email)
3. Enter content to generate QR code
4. Adjust style settings (optional)
5. Add logo (optional)
6. Download generated QR code

### Local Deployment
```bash
# Clone or download project files
# Start local server
python -m http.server 8000
# Or use Node.js
npx serve .
# Or use PHP
php -S localhost:8000

# Access in browser
http://localhost:8000
```

## 📋 Usage Instructions

### Content Settings
1. **Select Content Type**: Click top tabs to choose content type
2. **Enter Content**: Input content in corresponding input field
3. **Real-time Preview**: QR code generates automatically after input

### Style Configuration
1. **Error Correction Level**: Choose appropriate level based on use case
   - L Level: Suitable for clear environments, lowest density
   - M Level: Balanced choice, recommended
   - Q Level: Suitable for environments with possible minor damage
   - H Level: Highest error correction, suitable for harsh environments

2. **Color Settings**:
   - Use color picker or directly input hex color values
   - Support gradient effects with selectable directions

3. **Style Selection**:
   - Dot style affects basic QR code appearance
   - Corner style affects positioning marker appearance

### Logo Settings
1. **Enable Logo**: Check "Enable Logo" checkbox
2. **Select Source**:
   - Upload: Click or drag to upload custom image
   - Icons: Choose from 8 built-in icons
3. **Adjust Settings**: Adjust logo size and margin

### Download Options
1. **Single Download**: Choose PNG or SVG format
2. **Batch Download**:
   - Multi-size pack: Download 512px, 1024px, 1536px, 2048px sizes
   - Dual-format pack: Download both PNG and SVG formats

## 🛠️ Technology Stack

- **Frontend Framework**: Native HTML5 + CSS3 + JavaScript
- **Style Library**: TailwindCSS
- **QR Code Library**: qr-code-styling
- **Icon Library**: Font Awesome
- **Responsive**: CSS Grid + Flexbox

## 📱 Browser Compatibility

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- Mobile browsers

## 🔧 Custom Development

### Adding New Content Types
1. Add new tab and input area in HTML
2. Add corresponding processing logic in JavaScript
3. Update `handleContentChange` method

### Adding New Style Options
1. Add new control elements in HTML
2. Bind event listeners in JavaScript
3. Update `generateQRCodeInternal` method

### Adding New Logo Icons
1. Add new button in HTML
2. Add corresponding configuration in JavaScript's `logoMap`
3. Ensure icon is available in Font Awesome

## 📄 License

This project is open source under the MIT License and can be freely used and modified.

## 🤝 Contributing

Welcome to submit Issues and Pull Requests to improve this project!

## 📞 Contact

For questions or suggestions, please contact us through:
- Submit GitHub Issue
- Send email feedback

---

**Enjoy using the skygamedrop!** 🎉

## 📁 Project Structure

```
qrcode/
├── index.html          # Main page with complete UI
├── script.js           # JavaScript core functionality
├── Privacy.html        # Privacy policy page
├── Terms.html          # Terms of use page
├── demo.html           # Usage examples and best practices
├── README.md           # Project documentation
├── README_EN.md        # English documentation
└── images/
    └── logo.png        # Logo file
```

## 🎯 Key Features Highlights

- **Privacy First**: All processing happens locally in your browser
- **No Registration**: Use all features without creating an account
- **Professional Quality**: High-resolution output suitable for print
- **Mobile Optimized**: Perfect experience on all devices
- **Fast & Efficient**: Instant generation with real-time preview
- **Highly Customizable**: Extensive styling and branding options
