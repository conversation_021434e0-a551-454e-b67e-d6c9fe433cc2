# skygamedrop

A powerful online QR code generation tool with support for multiple formats and custom styling.

## 🌟 Features

### 📝 Content Support
- **Text Content** - Support for any text content
- **Website URLs** - Automatic recognition and formatting of URLs
- **Phone Numbers** - Generate callable phone QR codes
- **Email Addresses** - Generate email link QR codes

### 🎨 样式定制
- **容错级别** - L/M/Q/H四个级别可选
- **尺寸调节** - 200px到800px自由调节
- **颜色配置** - 前景色和背景色自定义
- **点样式** - 方形、圆角、圆点、经典等多种样式
- **边角样式** - 方形、圆角、圆点样式
- **渐变效果** - 支持线性渐变，多种方向可选
- **边距设置** - 0-50px边距调节

### 🖼️ Logo集成
- **文件上传** - 支持PNG、JPG、SVG格式
- **拖拽上传** - 便捷的拖拽上传体验
- **默认图标** - 8种内置图标可选
- **大小调节** - Logo大小和边距可调
- **实时预览** - 即时查看Logo效果

### 📥 下载功能
- **PNG格式** - 适合打印和分享
- **SVG格式** - 矢量格式，无损缩放
- **自定义尺寸** - 512px到2048px下载尺寸
- **批量下载** - 多尺寸包和双格式包
- **高质量输出** - 保证下载质量

### 📱 用户体验
- **实时预览** - 输入即时生成预览
- **响应式设计** - 完美适配移动端
- **状态指示** - 清晰的生成状态反馈
- **信息显示** - 显示二维码详细信息
- **工具提示** - 贴心的功能说明
- **键盘快捷键** - Ctrl+S快速下载

## 🚀 快速开始

### 在线使用
1. 打开 `index.html` 文件
2. 选择内容类型（文本/网址/电话/邮箱）
3. 输入要生成二维码的内容
4. 调整样式设置（可选）
5. 添加Logo（可选）
6. 下载生成的二维码

### 本地部署
```bash
# 克隆或下载项目文件
# 启动本地服务器
python -m http.server 8000
# 或使用Node.js
npx serve .
# 或使用PHP
php -S localhost:8000

# 在浏览器中访问
http://localhost:8000
```

## 📋 使用说明

### 内容设置
1. **选择内容类型**：点击顶部标签选择要生成的内容类型
2. **输入内容**：在对应的输入框中输入内容
3. **实时预览**：内容输入后会自动生成预览

### 样式配置
1. **容错级别**：根据使用场景选择合适的容错级别
   - L级：适合清晰环境，密度最低
   - M级：平衡选择，推荐使用
   - Q级：适合可能有轻微损坏的环境
   - H级：最高容错，适合恶劣环境

2. **颜色设置**：
   - 可以使用颜色选择器或直接输入十六进制颜色值
   - 支持渐变效果，可选择渐变方向

3. **样式选择**：
   - 点样式影响二维码的基本外观
   - 边角样式影响定位标记的外观

### Logo设置
1. **启用Logo**：勾选"启用Logo"复选框
2. **选择来源**：
   - 上传：点击或拖拽上传自定义图片
   - 默认：选择内置的8种图标之一
3. **调整设置**：调节Logo大小和边距

### 下载选项
1. **单个下载**：选择PNG或SVG格式下载
2. **批量下载**：
   - 多尺寸包：下载512px、1024px、1536px、2048px四种尺寸
   - 双格式包：同时下载PNG和SVG格式

## 🛠️ 技术栈

- **前端框架**：原生HTML5 + CSS3 + JavaScript
- **样式库**：TailwindCSS
- **二维码库**：qr-code-styling
- **图标库**：Font Awesome
- **响应式**：CSS Grid + Flexbox

## 📱 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- 移动端浏览器

## 🔧 自定义开发

### 添加新的内容类型
1. 在HTML中添加新的标签页和输入区域
2. 在JavaScript中添加对应的处理逻辑
3. 更新`handleContentChange`方法

### 添加新的样式选项
1. 在HTML中添加新的控制元素
2. 在JavaScript中绑定事件监听器
3. 更新`generateQRCodeInternal`方法

### 添加新的Logo图标
1. 在HTML中添加新的按钮
2. 在JavaScript的`logoMap`中添加对应配置
3. 确保图标在Font Awesome中可用

## 📄 许可证

本项目基于MIT许可证开源，可自由使用和修改。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件反馈

---

**享受使用二维码生成器！** 🎉
