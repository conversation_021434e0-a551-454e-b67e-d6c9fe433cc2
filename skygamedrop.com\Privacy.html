<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>Privacy Policy - Skygamedrop</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 主要图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer">
    <!-- 备用图标库 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.5.1/css/all.min.css" crossorigin="anonymous">
    <!-- 第三备用图标库 -->
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v6.5.1/css/all.css" crossorigin="anonymous">
    <!-- 引入更细致的字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        * {
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
            overflow-x: hidden;
            width: 100%;
        }

        body {
            max-width: 100vw;
            overflow-x: hidden;
            width: 100%;
            margin: 0;
            padding: 0;
            position: relative;
            font-family: 'Inter', 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            font-weight: 300;
            letter-spacing: -0.01em;
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        /* 防止所有容器超出视口宽度 */
        .container {
            max-width: 100%;
            width: 100%;
            margin: 0 auto;
            padding-left: 1rem;
            padding-right: 1rem;
        }

        /* 细致的字体样式设置 */
        h1, h2, h3, h4, h5, h6 {
            font-family: 'Poppins', 'Inter', sans-serif;
            font-weight: 300;
            letter-spacing: -0.02em;
            line-height: 1.2;
            color: #1f2937;
        }

        h1 {
            font-weight: 200;
            letter-spacing: -0.03em;
        }

        h2 {
            font-weight: 300;
            letter-spacing: -0.025em;
        }

        h3 {
            font-weight: 400;
            letter-spacing: -0.02em;
        }

        p {
            font-family: 'Inter', sans-serif;
            font-weight: 300;
            letter-spacing: -0.01em;
            line-height: 1.7;
            color: #4b5563;
        }

        /* 列表样式 */
        ul, ol {
            font-family: 'Inter', sans-serif;
            font-weight: 300;
            letter-spacing: -0.01em;
            line-height: 1.7;
        }

        li {
            color: #4b5563;
            margin-bottom: 0.5rem;
        }

        strong {
            font-weight: 500;
            color: #374151;
        }

        /* 导航和菜单的字体 */
        nav a {
            font-family: 'Inter', sans-serif;
            font-weight: 300;
            letter-spacing: -0.01em;
        }

        /* 链接样式 */
        a {
            font-family: 'Inter', sans-serif;
            font-weight: 300;
            letter-spacing: -0.005em;
            text-decoration: none;
            transition: all 0.2s ease;
        }

        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            margin-bottom: 2rem;
        }

        /* 图标兼容性修复 */
        .fas, .fa-solid {
            font-family: "Font Awesome 6 Free", "Font Awesome 5 Free", "FontAwesome";
            font-weight: 900;
            display: inline-block;
            font-style: normal;
            font-variant: normal;
            text-rendering: auto;
            line-height: 1;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .container {
                padding-left: 0.75rem;
                padding-right: 0.75rem;
                max-width: 100%;
                width: 100%;
            }

            .card {
                padding: 1rem;
                margin-bottom: 1rem;
                width: 100%;
                max-width: 100%;
            }

            .text-4xl {
                font-size: 2rem;
            }

            h1 {
                font-size: 2rem;
            }

            h2 {
                font-size: 1.5rem;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding-left: 0.5rem;
                padding-right: 0.5rem;
                max-width: 100%;
                width: 100%;
            }

            .card {
                padding: 0.75rem;
                margin-bottom: 0.75rem;
                width: 100%;
                max-width: 100%;
            }

            h1 {
                font-size: 1.75rem;
            }

            h2 {
                font-size: 1.25rem;
            }
        }

        /* 防止移动端水平滚动的额外措施 */
        @media (max-width: 768px) {
            html, body {
                touch-action: pan-y;
                -webkit-overflow-scrolling: touch;
            }

            header, section, footer {
                max-width: 100vw;
                overflow-x: hidden;
            }

            img {
                max-width: 100%;
                height: auto;
                object-fit: contain;
            }
        }
    </style>
</head>

<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-lg sticky top-0 z-50">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <img src="./images/logo.png" alt="Skygamedrop" class="h-10 w-auto">

                </div>
                <nav class="hidden md:flex space-x-6">
                    <a href="index.html#home" class="text-gray-600 hover:text-purple-600 transition-colors font-medium">Home</a>
                    <a href="about.html#about" class="text-gray-600 hover:text-purple-600 transition-colors font-medium">About</a>
                </nav>
                <!-- Mobile menu button -->
                <button class="md:hidden text-gray-600 hover:text-purple-600" id="mobile-menu-btn">
                    <i class="fa-solid fa-bars text-xl"></i>
                </button>
            </div>
            <!-- Mobile menu -->
            <div class="md:hidden mt-4 hidden" id="mobile-menu">
                <nav class="flex flex-col space-y-2">
                    <a href="index.html#home" class="text-gray-600 hover:text-purple-600 transition-colors py-2">Home</a>
                    <a href="about.html#about" class="text-gray-600 hover:text-purple-600 transition-colors py-2">About</a>
                </nav>
            </div>
        </div>
    </header>

    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <div class="card">
                <h1 class="text-4xl font-bold mb-6 text-gray-800">Privacy Policy</h1>
                <p class="text-gray-600 mb-8">Last updated: July 2025</p>

                <div class="space-y-8">
                    <section>
                        <h2 class="text-2xl font-semibold mb-4 text-gray-800">1. Information We Collect</h2>
                        <p class="text-gray-600 mb-4">
                            Skygamedrop is designed with privacy in mind. We collect minimal information to provide
                            our services:
                        </p>
                        <ul class="list-disc list-inside text-gray-600 space-y-2">
                            <li><strong>Content Data:</strong> The text, URLs, or other content you input to generate QR
                                codes is processed locally in your browser and is not transmitted to our servers.</li>
                            <li><strong>Usage Analytics:</strong> We may collect anonymous usage statistics to improve
                                our service, such as the number of QR codes generated and feature usage patterns.</li>
                            <li><strong>Technical Information:</strong> Basic technical information like browser type,
                                device type, and IP address for security and optimization purposes.</li>
                        </ul>
                    </section>

                    <section>
                        <h2 class="text-2xl font-semibold mb-4 text-gray-800">2. How We Use Your Information</h2>
                        <p class="text-gray-600 mb-4">We use the collected information for the following purposes:</p>
                        <ul class="list-disc list-inside text-gray-600 space-y-2">
                            <li>To provide and maintain our QR code generation service</li>
                            <li>To improve and optimize our website performance</li>
                            <li>To analyze usage patterns and enhance user experience</li>
                            <li>To ensure the security and integrity of our service</li>
                        </ul>
                    </section>

                    <section>
                        <h2 class="text-2xl font-semibold mb-4 text-gray-800">3. Data Processing and Storage</h2>
                        <p class="text-gray-600 mb-4">
                            <strong>Local Processing:</strong> All QR code generation happens locally in your browser.
                            Your input data is not sent to our servers unless you explicitly choose to save or share it.
                        </p>
                        <p class="text-gray-600 mb-4">
                            <strong>No Data Storage:</strong> We do not store the content you use to generate QR codes.
                            Once you close your browser or navigate away from the page, your data is automatically
                            deleted.
                        </p>
                    </section>

                    <section>
                        <h2 class="text-2xl font-semibold mb-4 text-gray-800">4. Cookies and Tracking</h2>
                        <p class="text-gray-600 mb-4">
                            We use minimal cookies and tracking technologies:
                        </p>
                        <ul class="list-disc list-inside text-gray-600 space-y-2">
                            <li><strong>Essential Cookies:</strong> Required for basic website functionality</li>
                            <li><strong>Analytics Cookies:</strong> Help us understand how visitors interact with our
                                website</li>
                            <li><strong>Preference Cookies:</strong> Remember your settings and preferences</li>
                        </ul>
                    </section>

                    <section>
                        <h2 class="text-2xl font-semibold mb-4 text-gray-800">5. Third-Party Services</h2>
                        <p class="text-gray-600 mb-4">
                            We may use third-party services for analytics and website optimization. These services have
                            their own privacy policies:
                        </p>
                        <ul class="list-disc list-inside text-gray-600 space-y-2">
                            <li>Google Analytics (for usage statistics)</li>
                            <li>CDN services for faster content delivery</li>
                        </ul>
                    </section>

                    <section>
                        <h2 class="text-2xl font-semibold mb-4 text-gray-800">6. Your Rights</h2>
                        <p class="text-gray-600 mb-4">You have the right to:</p>
                        <ul class="list-disc list-inside text-gray-600 space-y-2">
                            <li>Access any personal information we hold about you</li>
                            <li>Request correction of inaccurate information</li>
                            <li>Request deletion of your personal information</li>
                            <li>Object to processing of your personal information</li>
                            <li>Withdraw consent at any time</li>
                        </ul>
                    </section>

                    <section>
                        <h2 class="text-2xl font-semibold mb-4 text-gray-800">7. Security</h2>
                        <p class="text-gray-600 mb-4">
                            We implement appropriate security measures to protect your information. Since most
                            processing happens locally in your browser, your data remains under your control.
                        </p>
                    </section>

                    <section>
                        <h2 class="text-2xl font-semibold mb-4 text-gray-800">8. Changes to This Policy</h2>
                        <p class="text-gray-600 mb-4">
                            We may update this privacy policy from time to time. We will notify you of any changes by
                            posting the new policy on this page and updating the "Last updated" date.
                        </p>
                    </section>

                    <section>
                        <h2 class="text-2xl font-semibold mb-4 text-gray-800">9. Contact Us</h2>
                        <p class="text-gray-600 mb-4">
                            If you have any questions about this Privacy Policy, please contact us at: Skygamedrop.com
                        </p>
                       
                    </section>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
   <footer class="bg-gray-800 text-white py-12">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center space-x-4 mb-4">
                        <img src="./images/logo.png" alt="Skygamedrop" class="h-8 w-auto">

                    </div>
                    <p class="text-gray-300 mb-4">
                        The most comprehensive and user-friendly skygamedrop.com Create professional,
                        customizable QR codes for any purpose - completely free and privacy-focused.
                    </p>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="index.html#home" class="text-gray-300 hover:text-white transition-colors">Home</a></li>
                        <li><a href="about.html#about" class="text-gray-300 hover:text-white transition-colors">About</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-4">Legal</h3>
                    <ul class="space-y-2">
                        <li><a href="Privacy.html" class="text-gray-300 hover:text-white transition-colors">Privacy
                                Policy</a></li>
                        <li><a href="Terms.html" class="text-gray-300 hover:text-white transition-colors">Terms of
                                Use</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-700 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center text-white">
                <div class="mb-4 md:mb-0">
                    <p class="text-white">&copy; 2025 skygamedrop All rights reserved.</p>
                </div>
                <div class="text-sm text-gray-300">
                    <p class="text-gray-300">Built with ❤️ using modern web technologies</p>
                </div>
            </div>
        </div>
    </footer>
</body>
<script>
    // Mobile menu toggle
    document.getElementById('mobile-menu-btn').addEventListener('click', function () {
        const mobileMenu = document.getElementById('mobile-menu');
        mobileMenu.classList.toggle('hidden');
    });

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
                // Close mobile menu if open
                document.getElementById('mobile-menu').classList.add('hidden');
            }
        });
    });
</script>

</html>