// 二维码生成器主要功能
class QRCodeGenerator {
    constructor() {
        this.qrCode = null;
        this.currentContent = '';
        this.logoImage = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.initTabSwitching();
        this.updateSizeDisplay();
        this.updateLogoSizeDisplay();
        this.updateMarginDisplay();
        this.updateLogoMarginDisplay();
        this.updateDownloadSizeDisplay();
    }

    // 绑定事件监听器
    bindEvents() {
        // 内容输入事件
        document.getElementById('text-input').addEventListener('input', () => this.handleContentChange());
        document.getElementById('url-input').addEventListener('input', () => this.handleContentChange());
        document.getElementById('phone-input').addEventListener('input', () => this.handleContentChange());
        document.getElementById('email-input').addEventListener('input', () => this.handleContentChange());

        // 样式设置事件
        document.getElementById('error-level').addEventListener('change', () => this.generateQRCode());
        document.getElementById('qr-size').addEventListener('input', () => {
            this.updateSizeDisplay();
            this.generateQRCode();
        });
        document.getElementById('foreground-color').addEventListener('change', () => {
            this.syncColorPicker('foreground');
            this.generateQRCode();
        });
        document.getElementById('background-color').addEventListener('change', () => {
            this.syncColorPicker('background');
            this.generateQRCode();
        });
        document.getElementById('foreground-hex').addEventListener('input', () => {
            this.syncHexInput('foreground');
            this.generateQRCode();
        });
        document.getElementById('background-hex').addEventListener('input', () => {
            this.syncHexInput('background');
            this.generateQRCode();
        });
        document.getElementById('dot-style').addEventListener('change', () => this.generateQRCode());
        document.getElementById('corner-style').addEventListener('change', () => this.generateQRCode());
        document.getElementById('margin').addEventListener('input', () => {
            this.updateMarginDisplay();
            this.generateQRCode();
        });
        document.getElementById('enable-gradient').addEventListener('change', () => this.toggleGradientSettings());
        document.getElementById('gradient-start').addEventListener('change', () => this.generateQRCode());
        document.getElementById('gradient-end').addEventListener('change', () => this.generateQRCode());
        document.getElementById('gradient-direction').addEventListener('change', () => this.generateQRCode());

        // Logo相关事件
        document.getElementById('enable-logo').addEventListener('change', () => this.toggleLogoSettings());
        document.getElementById('logo-upload-area').addEventListener('click', () => {
            document.getElementById('logo-file').click();
        });
        document.getElementById('logo-file').addEventListener('change', (e) => this.handleLogoUpload(e));
        document.getElementById('remove-logo').addEventListener('click', () => this.removeLogo());
        document.getElementById('logo-size').addEventListener('input', () => {
            this.updateLogoSizeDisplay();
            this.generateQRCode();
        });
        document.getElementById('logo-margin').addEventListener('input', () => {
            this.updateLogoMarginDisplay();
            this.generateQRCode();
        });

        // Logo标签页切换
        document.querySelectorAll('[data-logo-tab]').forEach(button => {
            button.addEventListener('click', () => this.switchLogoTab(button.getAttribute('data-logo-tab')));
        });

        // 默认Logo选择
        document.querySelectorAll('.default-logo-btn').forEach(button => {
            button.addEventListener('click', () => this.selectDefaultLogo(button.getAttribute('data-logo')));
        });

        // 拖拽上传
        const uploadArea = document.getElementById('logo-upload-area');
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.processLogoFile(files[0]);
            }
        });

        // 下载事件
        document.getElementById('download-size').addEventListener('input', () => this.updateDownloadSizeDisplay());
        document.getElementById('download-png').addEventListener('click', () => this.downloadQRCode('png'));
        document.getElementById('download-svg').addEventListener('click', () => this.downloadQRCode('svg'));
        document.getElementById('download-all-sizes').addEventListener('click', () => this.downloadAllSizes());
        document.getElementById('download-both-formats').addEventListener('click', () => this.downloadBothFormats());
    }

    // 初始化标签页切换
    initTabSwitching() {
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content');

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                // 移除所有活动状态
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabContents.forEach(content => content.classList.remove('active'));

                // 添加当前活动状态
                button.classList.add('active');
                const tabId = button.getAttribute('data-tab') + '-tab';
                document.getElementById(tabId).classList.add('active');

                // 更新内容
                this.handleContentChange();
            });
        });
    }

    // 处理内容变化
    handleContentChange() {
        const activeTab = document.querySelector('.tab-button.active').getAttribute('data-tab');
        let content = '';

        switch (activeTab) {
            case 'text':
                content = document.getElementById('text-input').value;
                break;
            case 'url':
                content = document.getElementById('url-input').value;
                break;
            case 'phone':
                const phone = document.getElementById('phone-input').value;
                content = phone ? `tel:${phone}` : '';
                break;
            case 'email':
                const email = document.getElementById('email-input').value;
                content = email ? `mailto:${email}` : '';
                break;
        }

        this.currentContent = content;
        this.generateQRCode();
    }

    // 生成二维码
    generateQRCode() {
        if (!this.currentContent.trim()) {
            this.showEmptyState();
            this.toggleDownloadButtons(false);
            this.updateStatus('Waiting for input', 'gray');
            this.hideQRInfo();
            return;
        }

        this.updateStatus('Generating...', 'blue');

        // 添加短暂延迟以显示加载状态
        setTimeout(() => {
            this.generateQRCodeInternal();
        }, 100);
    }

    // 内部生成二维码方法
    generateQRCodeInternal() {
        const size = parseInt(document.getElementById('qr-size').value);
        const errorLevel = document.getElementById('error-level').value;
        const foregroundColor = document.getElementById('foreground-color').value;
        const backgroundColor = document.getElementById('background-color').value;
        const dotStyle = document.getElementById('dot-style').value;
        const cornerStyle = document.getElementById('corner-style').value;
        const margin = parseInt(document.getElementById('margin').value);
        const enableLogo = document.getElementById('enable-logo').checked;
        const enableGradient = document.getElementById('enable-gradient').checked;

        let dotsColor = foregroundColor;

        // 处理渐变效果
        if (enableGradient) {
            const gradientStart = document.getElementById('gradient-start').value;
            const gradientEnd = document.getElementById('gradient-end').value;
            const gradientDirection = document.getElementById('gradient-direction').value;

            dotsColor = {
                type: 'linear-gradient',
                rotation: parseInt(gradientDirection),
                colorStops: [
                    { offset: 0, color: gradientStart },
                    { offset: 1, color: gradientEnd }
                ]
            };
        }

        const options = {
            width: size,
            height: size,
            data: this.currentContent,
            margin: margin,
            dotsOptions: {
                color: dotsColor,
                type: dotStyle
            },
            backgroundOptions: {
                color: backgroundColor,
            },
            cornersSquareOptions: {
                color: enableGradient ? dotsColor : foregroundColor,
                type: cornerStyle
            },
            cornersDotOptions: {
                color: enableGradient ? dotsColor : foregroundColor,
                type: "dot"
            },
            qrOptions: {
                errorCorrectionLevel: errorLevel
            }
        };

        // 添加Logo配置
        if (enableLogo && this.logoImage) {
            const logoSize = parseFloat(document.getElementById('logo-size').value);
            const logoMargin = parseInt(document.getElementById('logo-margin').value);
            options.imageOptions = {
                crossOrigin: "anonymous",
                margin: logoMargin,
                imageSize: logoSize
            };
            options.image = this.logoImage;
        }

        // 清除之前的二维码
        const previewContainer = document.getElementById('qr-preview');
        previewContainer.innerHTML = '';

        try {
            // 创建新的二维码
            this.qrCode = new QRCodeStyling(options);
            this.qrCode.append(previewContainer);

            this.toggleDownloadButtons(true);
            this.updateStatus('Generated successfully', 'green');
            this.showQRInfo();
        } catch (error) {
            console.error('生成二维码时出错:', error);
            this.updateStatus('Generation failed', 'red');
            this.showEmptyState();
            this.toggleDownloadButtons(false);
        }
    }

    // 显示空状态
    showEmptyState() {
        const previewContainer = document.getElementById('qr-preview');
        previewContainer.innerHTML = `
            <div class="text-center text-gray-500">
                <i class="fas fa-qrcode text-6xl mb-4"></i>
                <p>Enter content to generate QR code</p>
            </div>
        `;
    }

    // 切换Logo设置显示
    toggleLogoSettings() {
        const enableLogo = document.getElementById('enable-logo').checked;
        const logoSettings = document.getElementById('logo-settings');
        
        if (enableLogo) {
            logoSettings.style.display = 'block';
        } else {
            logoSettings.style.display = 'none';
            this.logoImage = null;
        }
        
        this.generateQRCode();
    }

    // 处理Logo上传
    handleLogoUpload(event) {
        const file = event.target.files[0];
        if (file) {
            this.processLogoFile(file);
        }
    }

    // 处理Logo文件
    processLogoFile(file) {
        if (!file.type.startsWith('image/')) {
            alert('Please select a valid image file!');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            this.logoImage = e.target.result;
            this.showLogoPreview(e.target.result);
            this.generateQRCode();
        };
        reader.readAsDataURL(file);
    }

    // 显示Logo预览
    showLogoPreview(imageSrc) {
        const previewContainer = document.getElementById('logo-preview-container');
        const previewImage = document.getElementById('logo-preview');
        
        previewImage.src = imageSrc;
        previewContainer.style.display = 'block';
    }

    // 移除Logo
    removeLogo() {
        this.logoImage = null;
        document.getElementById('logo-preview-container').style.display = 'none';
        document.getElementById('logo-file').value = '';
        this.generateQRCode();
    }

    // 更新尺寸显示
    updateSizeDisplay() {
        const size = document.getElementById('qr-size').value;
        document.getElementById('size-value').textContent = size;
    }

    // 更新Logo尺寸显示
    updateLogoSizeDisplay() {
        const logoSize = document.getElementById('logo-size').value;
        document.getElementById('logo-size-value').textContent = logoSize;
    }

    // 更新边距显示
    updateMarginDisplay() {
        const margin = document.getElementById('margin').value;
        document.getElementById('margin-value').textContent = margin;
    }

    // 同步颜色选择器和文本输入
    syncColorPicker(type) {
        const colorPicker = document.getElementById(`${type}-color`);
        const hexInput = document.getElementById(`${type}-hex`);
        hexInput.value = colorPicker.value;
    }

    // 同步十六进制输入和颜色选择器
    syncHexInput(type) {
        const colorPicker = document.getElementById(`${type}-color`);
        const hexInput = document.getElementById(`${type}-hex`);
        const hexValue = hexInput.value;

        // 验证十六进制颜色格式
        if (/^#[0-9A-F]{6}$/i.test(hexValue)) {
            colorPicker.value = hexValue;
        }
    }

    // 切换渐变设置显示
    toggleGradientSettings() {
        const enableGradient = document.getElementById('enable-gradient').checked;
        const gradientSettings = document.getElementById('gradient-settings');

        if (enableGradient) {
            gradientSettings.style.display = 'block';
        } else {
            gradientSettings.style.display = 'none';
        }

        this.generateQRCode();
    }

    // 更新Logo边距显示
    updateLogoMarginDisplay() {
        const logoMargin = document.getElementById('logo-margin').value;
        document.getElementById('logo-margin-value').textContent = logoMargin;
    }

    // 切换Logo标签页
    switchLogoTab(tabName) {
        // 移除所有活动状态
        document.querySelectorAll('[data-logo-tab]').forEach(btn => btn.classList.remove('active'));
        document.querySelectorAll('.logo-tab-content').forEach(content => content.classList.remove('active'));

        // 添加当前活动状态
        document.querySelector(`[data-logo-tab="${tabName}"]`).classList.add('active');
        document.getElementById(`${tabName}-logo-tab`).classList.add('active');

        // 如果切换到默认Logo标签页，清除上传的Logo
        if (tabName === 'default') {
            this.removeLogo();
        }
    }

    // 选择默认Logo
    selectDefaultLogo(logoType) {
        // 移除所有选中状态
        document.querySelectorAll('.default-logo-btn').forEach(btn => btn.classList.remove('selected'));

        // 添加选中状态
        document.querySelector(`[data-logo="${logoType}"]`).classList.add('selected');

        // 创建SVG Logo
        this.createDefaultLogo(logoType);
    }

    // 创建默认Logo SVG
    createDefaultLogo(logoType) {
        const logoMap = {
            heart: { icon: '♥', color: '#ef4444' },
            star: { icon: '★', color: '#eab308' },
            home: { icon: '🏠', color: '#3b82f6' },
            phone: { icon: '📞', color: '#10b981' },
            email: { icon: '✉', color: '#8b5cf6' },
            location: { icon: '📍', color: '#dc2626' },
            wifi: { icon: '📶', color: '#2563eb' },
            shopping: { icon: '🛒', color: '#f97316' }
        };

        const logo = logoMap[logoType];
        if (!logo) return;

        // 创建SVG字符串
        const svgString = `
            <svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
                <rect width="100" height="100" fill="white" rx="10"/>
                <text x="50" y="65" font-size="60" text-anchor="middle" fill="${logo.color}">${logo.icon}</text>
            </svg>
        `;

        // 转换为Data URL
        const svgBlob = new Blob([svgString], { type: 'image/svg+xml' });
        const url = URL.createObjectURL(svgBlob);

        this.logoImage = url;
        this.showLogoPreview(url);
        this.generateQRCode();
    }

    // 更新状态指示器
    updateStatus(message, color) {
        const statusElement = document.getElementById('qr-status');
        const colorMap = {
            gray: 'text-gray-500',
            blue: 'text-blue-500',
            green: 'text-green-500',
            red: 'text-red-500'
        };

        const iconMap = {
            gray: 'fas fa-circle',
            blue: 'fas fa-spinner fa-spin',
            green: 'fas fa-check-circle',
            red: 'fas fa-exclamation-circle'
        };

        statusElement.className = `text-sm font-medium ${colorMap[color] || 'text-gray-500'}`;
        statusElement.innerHTML = `<i class="${iconMap[color] || 'fas fa-circle'} mr-1"></i>${message}`;
    }

    // 显示二维码信息
    showQRInfo() {
        const infoElement = document.getElementById('qr-info');
        const dataLength = this.currentContent.length;
        const errorLevel = document.getElementById('error-level').value;

        // 更新信息
        document.getElementById('data-length').textContent = dataLength;
        document.getElementById('current-error-level').textContent = errorLevel;

        // 估算二维码版本（简化计算）
        let version = 1;
        if (dataLength > 25) version = 2;
        if (dataLength > 47) version = 3;
        if (dataLength > 77) version = 4;
        if (dataLength > 114) version = 5;
        if (dataLength > 154) version = 6;
        if (dataLength > 195) version = 7;
        if (dataLength > 224) version = 8;
        if (dataLength > 279) version = 9;
        if (dataLength > 335) version = 10;

        document.getElementById('qr-version').textContent = version;

        // 计算模块数
        const moduleCount = 17 + 4 * version;
        document.getElementById('module-count').textContent = `${moduleCount}×${moduleCount}`;

        infoElement.style.display = 'block';
    }

    // 隐藏二维码信息
    hideQRInfo() {
        const infoElement = document.getElementById('qr-info');
        infoElement.style.display = 'none';
    }

    // 切换下载按钮状态
    toggleDownloadButtons(enabled) {
        const buttons = [
            'download-png',
            'download-svg',
            'download-all-sizes',
            'download-both-formats'
        ];

        buttons.forEach(buttonId => {
            const button = document.getElementById(buttonId);
            button.disabled = !enabled;

            if (enabled) {
                button.classList.remove('opacity-50', 'cursor-not-allowed');
            } else {
                button.classList.add('opacity-50', 'cursor-not-allowed');
            }
        });
    }

    // 更新下载尺寸显示
    updateDownloadSizeDisplay() {
        const downloadSize = document.getElementById('download-size').value;
        document.getElementById('download-size-value').textContent = downloadSize;
    }

    // 下载二维码
    downloadQRCode(format) {
        if (!this.qrCode) {
            alert('Please generate a QR code first!');
            return;
        }

        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        const filename = `qrcode_${timestamp}`;
        const downloadSize = parseInt(document.getElementById('download-size').value);

        // 创建临时的高分辨率二维码用于下载
        const downloadOptions = this.getQRCodeOptions();
        downloadOptions.width = downloadSize;
        downloadOptions.height = downloadSize;

        const tempQRCode = new QRCodeStyling(downloadOptions);

        if (format === 'png') {
            tempQRCode.download({
                name: filename,
                extension: 'png'
            });
        } else if (format === 'svg') {
            tempQRCode.download({
                name: filename,
                extension: 'svg'
            });
        }
    }

    // 获取当前二维码配置选项
    getQRCodeOptions() {
        const size = parseInt(document.getElementById('qr-size').value);
        const errorLevel = document.getElementById('error-level').value;
        const foregroundColor = document.getElementById('foreground-color').value;
        const backgroundColor = document.getElementById('background-color').value;
        const dotStyle = document.getElementById('dot-style').value;
        const cornerStyle = document.getElementById('corner-style').value;
        const margin = parseInt(document.getElementById('margin').value);
        const enableLogo = document.getElementById('enable-logo').checked;
        const enableGradient = document.getElementById('enable-gradient').checked;

        let dotsColor = foregroundColor;

        // 处理渐变效果
        if (enableGradient) {
            const gradientStart = document.getElementById('gradient-start').value;
            const gradientEnd = document.getElementById('gradient-end').value;
            const gradientDirection = document.getElementById('gradient-direction').value;

            dotsColor = {
                type: 'linear-gradient',
                rotation: parseInt(gradientDirection),
                colorStops: [
                    { offset: 0, color: gradientStart },
                    { offset: 1, color: gradientEnd }
                ]
            };
        }

        const options = {
            width: size,
            height: size,
            data: this.currentContent,
            margin: margin,
            dotsOptions: {
                color: dotsColor,
                type: dotStyle
            },
            backgroundOptions: {
                color: backgroundColor,
            },
            cornersSquareOptions: {
                color: enableGradient ? dotsColor : foregroundColor,
                type: cornerStyle
            },
            cornersDotOptions: {
                color: enableGradient ? dotsColor : foregroundColor,
                type: "dot"
            },
            qrOptions: {
                errorCorrectionLevel: errorLevel
            }
        };

        // 添加Logo配置
        if (enableLogo && this.logoImage) {
            const logoSize = parseFloat(document.getElementById('logo-size').value);
            const logoMargin = parseInt(document.getElementById('logo-margin').value);
            options.imageOptions = {
                crossOrigin: "anonymous",
                margin: logoMargin,
                imageSize: logoSize
            };
            options.image = this.logoImage;
        }

        return options;
    }

    // 下载多种尺寸
    async downloadAllSizes() {
        if (!this.qrCode) {
            alert('Please generate a QR code first!');
            return;
        }

        const sizes = [512, 1024, 1536, 2048];
        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');

        // 这里可以添加ZIP打包功能，目前简化为依次下载
        for (const size of sizes) {
            const options = this.getQRCodeOptions();
            options.width = size;
            options.height = size;

            const tempQRCode = new QRCodeStyling(options);
            tempQRCode.download({
                name: `qrcode_${size}px_${timestamp}`,
                extension: 'png'
            });

            // 添加延迟避免浏览器阻止多个下载
            await new Promise(resolve => setTimeout(resolve, 500));
        }
    }

    // 下载PNG和SVG两种格式
    async downloadBothFormats() {
        if (!this.qrCode) {
            alert('Please generate a QR code first!');
            return;
        }

        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        const downloadSize = parseInt(document.getElementById('download-size').value);

        const options = this.getQRCodeOptions();
        options.width = downloadSize;
        options.height = downloadSize;

        const tempQRCode = new QRCodeStyling(options);

        // 下载PNG
        tempQRCode.download({
            name: `qrcode_${timestamp}`,
            extension: 'png'
        });

        // 延迟后下载SVG
        setTimeout(() => {
            tempQRCode.download({
                name: `qrcode_${timestamp}`,
                extension: 'svg'
            });
        }, 500);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.qrGenerator = new QRCodeGenerator();
});

// 添加一些实用功能
document.addEventListener('DOMContentLoaded', () => {
    // 添加键盘快捷键支持
    document.addEventListener('keydown', (e) => {
        // Ctrl+S 下载PNG
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            const pngButton = document.getElementById('download-png');
            if (!pngButton.disabled) {
                pngButton.click();
            }
        }
    });

    // 添加示例内容按钮
    const addExampleButtons = () => {
        const textTab = document.getElementById('text-tab');
        const urlTab = document.getElementById('url-tab');
        
        // 为文本标签页添加示例按钮
        const textExample = document.createElement('button');
        textExample.className = 'btn btn-secondary mt-2';
        textExample.innerHTML = '<i class="fas fa-lightbulb mr-1"></i>Try Example';
        textExample.onclick = () => {
            // 切换到文本标签页
            document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            document.querySelector('[data-tab="text"]').classList.add('active');
            document.getElementById('text-tab').classList.add('active');

            // 设置示例文本
            document.getElementById('text-input').value = 'Welcome to our Skygamedrop!\nThis is a powerful online tool for creating custom QR codes.';

            // 触发内容变化
            if (window.qrGenerator) {
                window.qrGenerator.handleContentChange();
            }
        };
        textTab.appendChild(textExample);

        // 为网址标签页添加示例按钮
        const urlExample = document.createElement('button');
        urlExample.className = 'btn btn-secondary mt-2';
        urlExample.innerHTML = '<i class="fas fa-lightbulb mr-1"></i>Try Example';
        urlExample.onclick = () => {
            // 切换到网址标签页
            document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            document.querySelector('[data-tab="url"]').classList.add('active');
            document.getElementById('url-tab').classList.add('active');

            // 设置示例网址
            document.getElementById('url-input').value = 'https://github.com';

            // 触发内容变化
            if (window.qrGenerator) {
                window.qrGenerator.handleContentChange();
            }
        };
        urlTab.appendChild(urlExample);
    };

    addExampleButtons();
});
