<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>skygamedrop - Create Custom QR Codes Online</title>
    <meta name="description"
        content="Free online skygamedrop. Create custom QR codes with logos, colors, and various styles. Generate QR codes for URLs, text, phone numbers, emails, and more.">
    <meta name="keywords" content="skygamedrop, QR code maker, custom QR codes, free QR generator, QR code with logo">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/qr-code-styling@1.6.0-rc.1/lib/qr-code-styling.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- 引入更细致的字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        * {
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
            overflow-x: hidden;
            width: 100%;
        }

        body {
            max-width: 100vw;
            overflow-x: hidden;
            width: 100%;
            margin: 0;
            padding: 0;
            position: relative;
            font-family: 'Inter', 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            font-weight: 300;
            letter-spacing: -0.01em;
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        /* 防止所有容器超出视口宽度 */
        .container {
            max-width: 100%;
            width: 100%;
            margin: 0 auto;
            padding-left: 1rem;
            padding-right: 1rem;
        }

        /* 细致的字体样式设置 */
        h1, h2, h3, h4, h5, h6 {
            font-family: 'Poppins', 'Inter', sans-serif;
            font-weight: 300;
            letter-spacing: -0.02em;
            line-height: 1.2;
            color: #1f2937;
        }

        h1 {
            font-weight: 200;
            letter-spacing: -0.03em;
        }

        h2 {
            font-weight: 300;
            letter-spacing: -0.025em;
        }

        h3 {
            font-weight: 400;
            letter-spacing: -0.02em;
        }

        p {
            font-family: 'Inter', sans-serif;
            font-weight: 300;
            letter-spacing: -0.01em;
            line-height: 1.7;
            color: #4b5563;
        }

        .text-xl, .text-2xl {
            font-weight: 300;
            letter-spacing: -0.015em;
        }

        .text-3xl, .text-4xl {
            font-weight: 200;
            letter-spacing: -0.025em;
        }

        .text-5xl, .text-6xl {
            font-weight: 100;
            letter-spacing: -0.03em;
        }

        /* 按钮和交互元素的字体 */
        button, .btn {
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            letter-spacing: -0.005em;
        }

        /* 表单元素的字体 */
        input, textarea, select {
            font-family: 'Inter', sans-serif;
            font-weight: 300;
            letter-spacing: -0.01em;
        }

        /* 导航和菜单的字体 */
        nav a {
            font-family: 'Inter', sans-serif;
            font-weight: 300;
            letter-spacing: -0.01em;
        }

        /* 标签的字体 */
        .label {
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            letter-spacing: -0.005em;
            color: #374151;
        }

        /* 小文本的字体 */
        .text-sm {
            font-weight: 300;
            letter-spacing: -0.005em;
        }

        /* 状态文本的字体 */
        .font-medium {
            font-weight: 400;
        }

        .font-semibold {
            font-weight: 500;
        }

        .font-bold {
            font-weight: 600;
        }

        /* 特殊元素的字体优化 */
        .hero-title {
            font-family: 'Poppins', sans-serif;
            font-weight: 100;
            letter-spacing: -0.04em;
            line-height: 1.1;
        }

        .feature-title {
            font-family: 'Poppins', sans-serif;
            font-weight: 400;
            letter-spacing: -0.015em;
        }

        .card-title {
            font-family: 'Poppins', sans-serif;
            font-weight: 300;
            letter-spacing: -0.02em;
        }

        /* 状态和提示文本 */
        .status-text {
            font-family: 'Inter', sans-serif;
            font-weight: 300;
            letter-spacing: -0.005em;
        }

        /* 数值显示 */
        .value-display {
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            letter-spacing: -0.01em;
            font-variant-numeric: tabular-nums;
        }

        /* 链接样式 */
        a {
            font-family: 'Inter', sans-serif;
            font-weight: 300;
            letter-spacing: -0.005em;
            text-decoration: none;
            transition: all 0.2s ease;
        }

        /* 工具提示文本 */
        .tooltip::before {
            font-family: 'Inter', sans-serif;
            font-weight: 300;
            letter-spacing: -0.005em;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .qr-preview {
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .color-picker {
            width: 50px;
            height: 40px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
        }

        .input-group {
            margin-bottom: 1rem;
        }

        .label {
            display: block;
            margin-bottom: 0.5rem;
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            letter-spacing: -0.005em;
            color: #374151;
        }

        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-family: 'Inter', sans-serif;
            font-size: 1rem;
            font-weight: 300;
            letter-spacing: -0.01em;
            line-height: 1.5;
            transition: border-color 0.3s ease;
            color: #1f2937;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            font-size: 0.95rem;
            letter-spacing: -0.005em;
            transition: all 0.3s ease;
            cursor: pointer;
            border: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
            border: 2px solid #e5e7eb;
        }

        .btn-secondary:hover {
            background: #e5e7eb;
        }

        .card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .range-slider {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: #e5e7eb;
            outline: none;
            -webkit-appearance: none;
        }

        .range-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
        }

        .range-slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
            border: none;
        }

        .tab-button {
            padding: 0.75rem 1.5rem;
            border: none;
            background: transparent;
            color: #6b7280;
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            font-size: 0.9rem;
            letter-spacing: -0.005em;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
        }

        .tab-button.active {
            color: #667eea;
            border-bottom-color: #667eea;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .logo-upload-area {
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .logo-upload-area:hover {
            border-color: #667eea;
            background: #f8faff;
        }

        .logo-upload-area.dragover {
            border-color: #667eea;
            background: #f0f4ff;
        }

        .logo-preview {
            max-width: 100px;
            max-height: 100px;
            border-radius: 8px;
            margin: 1rem auto;
        }

        .logo-tab-content {
            display: none;
        }

        .logo-tab-content.active {
            display: block;
        }

        .default-logo-btn {
            padding: 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .default-logo-btn:hover {
            border-color: #667eea;
            background: #f8faff;
            transform: translateY(-2px);
        }

        .default-logo-btn.selected {
            border-color: #667eea;
            background: #f0f4ff;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {

            0%,
            100% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.05);
            }
        }

        .fade-in {
            animation: fadeIn 0.6s ease-out;
        }

        .pulse-animation {
            animation: pulse 2s infinite;
        }

        /* 加载状态 */
        .loading {
            position: relative;
            overflow: hidden;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% {
                left: -100%;
            }

            100% {
                left: 100%;
            }
        }

        /* 工具提示 */
        .tooltip {
            position: relative;
            cursor: help;
        }

        .tooltip::before {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 125%;
            left: 50%;
            transform: translateX(-50%);
            background: #1f2937;
            color: white;
            padding: 0.5rem;
            border-radius: 4px;
            font-size: 0.875rem;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .tooltip::after {
            content: '';
            position: absolute;
            bottom: 115%;
            left: 50%;
            transform: translateX(-50%);
            border: 5px solid transparent;
            border-top-color: #1f2937;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .tooltip:hover::before,
        .tooltip:hover::after {
            opacity: 1;
            visibility: visible;
        }

        /* 成功状态 */
        .success-state {
            border: 2px solid #10b981;
            background: #f0fdf4;
        }

        /* 错误状态 */
        .error-state {
            border: 2px solid #ef4444;
            background: #fef2f2;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .container {
                padding-left: 0.75rem;
                padding-right: 0.75rem;
                max-width: 100%;
                width: 100%;
            }

            .card {
                padding: 1rem;
                margin-bottom: 1rem;
                width: 100%;
                max-width: 100%;
            }

            .grid-cols-2 {
                grid-template-columns: 1fr;
            }

            .lg\\:grid-cols-2 {
                grid-template-columns: 1fr;
            }

            .md\\:grid-cols-2 {
                grid-template-columns: 1fr;
            }

            .md\\:grid-cols-3 {
                grid-template-columns: 1fr;
            }

            .lg\\:grid-cols-4 {
                grid-template-columns: repeat(2, 1fr);
            }

            .text-4xl {
                font-size: 2rem;
            }

            .text-5xl {
                font-size: 2.5rem;
            }

            .md\\:text-6xl {
                font-size: 2.5rem;
            }

            .md\\:text-4xl {
                font-size: 2rem;
            }

            .qr-preview {
                min-height: 250px;
                width: 100%;
                max-width: 100%;
            }

            .default-logo-btn {
                padding: 0.75rem;
            }

            /* 防止表单元素超出容器 */
            .form-input, .range-slider, .color-picker {
                max-width: 100%;
                width: 100%;
            }

            .color-picker {
                width: 50px;
                flex-shrink: 0;
            }

            /* 修复按钮组布局 */
            .flex.flex-wrap.gap-2 {
                flex-wrap: wrap;
                gap: 0.5rem;
            }

            .tab-button {
                flex: 1;
                min-width: 0;
                padding: 0.5rem 0.75rem;
                font-size: 0.875rem;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding-left: 0.5rem;
                padding-right: 0.5rem;
                max-width: 100%;
                width: 100%;
            }

            .card {
                padding: 0.75rem;
                margin-bottom: 0.75rem;
                width: 100%;
                max-width: 100%;
            }

            .btn {
                padding: 0.5rem 1rem;
                font-size: 0.875rem;
                width: 100%;
                max-width: 100%;
            }

            .color-picker {
                width: 40px;
                height: 35px;
                flex-shrink: 0;
            }

            /* 进一步优化小屏幕 */
            .grid.grid-cols-4.gap-2 {
                grid-template-columns: repeat(3, 1fr);
                gap: 0.5rem;
            }

            .text-xl {
                font-size: 1.125rem;
            }

            .md\\:text-2xl {
                font-size: 1.25rem;
            }

            /* 确保所有元素不超出屏幕 */
            * {
                max-width: 100%;
                word-wrap: break-word;
                overflow-wrap: break-word;
            }

            img {
                max-width: 100%;
                height: auto;
            }
        }

        /* 额外的移动端优化 */
        @media (max-width: 375px) {
            .container {
                padding-left: 0.25rem;
                padding-right: 0.25rem;
            }

            .card {
                padding: 0.5rem;
            }

            .grid.grid-cols-4.gap-2 {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* 防止移动端水平滚动的额外措施 */
        @media (max-width: 768px) {
            /* 防止触摸滑动导致的水平滚动 */
            html, body {
                touch-action: pan-y;
                -webkit-overflow-scrolling: touch;
            }

            /* 确保所有主要容器不超出视口 */
            header, section, footer {
                max-width: 100vw;
                overflow-x: hidden;
            }

            /* 修复可能导致水平滚动的元素 */
            .flex {
                flex-wrap: wrap;
            }

            .space-x-4 > * + * {
                margin-left: 0.5rem;
            }

            .space-x-6 > * + * {
                margin-left: 0.75rem;
            }

            /* 确保导航菜单不会超出屏幕 */
            nav {
                max-width: 100%;
                overflow-x: hidden;
            }

            /* 修复可能的表格或预格式化文本溢出 */
            pre, code {
                white-space: pre-wrap;
                word-break: break-all;
                overflow-wrap: break-word;
            }

            /* 确保图片不会导致水平滚动 */
            img {
                max-width: 100%;
                height: auto;
                object-fit: contain;
            }
        }
    </style>
</head>

<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-lg sticky top-0 z-50">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <img src="./images/logo.png" alt="skygamedrop" class="h-10 w-auto">

                </div>
                <nav class="hidden md:flex space-x-6">
                    <a href="index.html#home"
                        class="text-gray-600 hover:text-purple-600 transition-colors font-medium">Home</a>
                    <a href="about.html#about"
                        class="text-gray-600 hover:text-purple-600 transition-colors font-medium">About</a>
                </nav>
                <!-- Mobile menu button -->
                <button class="md:hidden text-gray-600 hover:text-purple-600" id="mobile-menu-btn">
                    <i class="fas fa-bars text-xl"></i>
                </button>
            </div>
            <!-- Mobile menu -->
            <div class="md:hidden mt-4 hidden" id="mobile-menu">
                <nav class="flex flex-col space-y-2">
                    <a href="index.html#home"
                        class="text-gray-600 hover:text-purple-600 transition-colors py-2">Home</a>
                    <a href="about.html#about"
                        class="text-gray-600 hover:text-purple-600 transition-colors py-2">About</a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section id="home" class="gradient-bg py-16">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-4xl md:text-6xl hero-title text-white mb-6">
                <i class="fas fa-qrcode mr-4"></i>skygamedrop
            </h1>
            <p class="text-xl md:text-2xl text-white opacity-90 mb-8 max-w-3xl mx-auto">
                Create professional, customizable QR codes instantly. Add logos, customize colors, and download in
                multiple formats - all for free!
            </p>
            <div class="flex flex-col sm:flex-row gap-2 sm:gap-4 justify-center items-center flex-wrap">
                <div class="flex items-center text-white opacity-80 text-sm sm:text-base">
                    <i class="fas fa-check-circle mr-2"></i>
                    <span>100% Free</span>
                </div>
                <div class="flex items-center text-white opacity-80 text-sm sm:text-base">
                    <i class="fas fa-check-circle mr-2"></i>
                    <span>No Registration Required</span>
                </div>
                <div class="flex items-center text-white opacity-80 text-sm sm:text-base">
                    <i class="fas fa-check-circle mr-2"></i>
                    <span>High Quality Output</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl feature-title text-gray-800 mb-4">Powerful Features</h2>
                <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                    Everything you need to create professional QR codes for any purpose
                </p>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="text-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-palette text-2xl text-blue-600"></i>
                    </div>
                    <h3 class="text-xl feature-title mb-2">Custom Styling</h3>
                    <p class="text-gray-600">Customize colors, patterns, and add your logo for branded QR codes</p>
                </div>
                <div class="text-center">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-download text-2xl text-green-600"></i>
                    </div>
                    <h3 class="text-xl feature-title mb-2">Multiple Formats</h3>
                    <p class="text-gray-600">Download as PNG, SVG, or get multiple sizes in one package</p>
                </div>
                <div class="text-center">
                    <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-mobile-alt text-2xl text-purple-600"></i>
                    </div>
                    <h3 class="text-xl feature-title mb-2">Mobile Friendly</h3>
                    <p class="text-gray-600">Fully responsive design that works perfectly on all devices</p>
                </div>
                <div class="text-center">
                    <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-shield-alt text-2xl text-red-600"></i>
                    </div>
                    <h3 class="text-xl feature-title mb-2">Privacy First</h3>
                    <p class="text-gray-600">All processing happens locally - your data never leaves your browser</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Generator Section -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">

            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl feature-title text-gray-800 mb-4">Create Your QR Code</h2>
                <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                    Choose your content type, customize the design, and generate your QR code in seconds
                </p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Left Panel - Configuration -->
                <div class="space-y-6">
                    <!-- Content Input -->
                    <div class="card">
                        <h2 class="text-2xl card-title mb-4 text-gray-800">
                            <i class="fas fa-edit mr-2"></i>Content Settings
                        </h2>

                        <!-- Content Type Selection -->
                        <div class="input-group">
                            <label class="label">Content Type</label>
                            <div class="grid grid-cols-2 sm:flex sm:flex-wrap gap-2">
                                <button class="tab-button active" data-tab="text">
                                    <i class="fas fa-font mr-1"></i><span class="hidden sm:inline">Text</span><span class="sm:hidden">Text</span>
                                </button>
                                <button class="tab-button" data-tab="url">
                                    <i class="fas fa-link mr-1"></i><span class="hidden sm:inline">URL</span><span class="sm:hidden">URL</span>
                                </button>
                                <button class="tab-button" data-tab="phone">
                                    <i class="fas fa-phone mr-1"></i><span class="hidden sm:inline">Phone</span><span class="sm:hidden">Phone</span>
                                </button>
                                <button class="tab-button" data-tab="email">
                                    <i class="fas fa-envelope mr-1"></i><span class="hidden sm:inline">Email</span><span class="sm:hidden">Email</span>
                                </button>
                            </div>
                        </div>

                        <!-- Content Input Areas -->
                        <div class="tab-content active" id="text-tab">
                            <div class="input-group">
                                <label class="label" for="text-input">Enter Text Content</label>
                                <textarea id="text-input" class="form-input" rows="4"
                                    placeholder="Enter the text content for your QR code..."></textarea>
                                <p class="text-sm text-gray-500 mt-1">Perfect for messages, instructions, or any text
                                    information</p>
                            </div>
                        </div>

                        <div class="tab-content" id="url-tab">
                            <div class="input-group">
                                <label class="label" for="url-input">Enter Website URL</label>
                                <input type="url" id="url-input" class="form-input" placeholder="https://example.com">
                                <p class="text-sm text-gray-500 mt-1">Create QR codes that link directly to websites,
                                    social media, or online resources</p>
                            </div>
                        </div>

                        <div class="tab-content" id="phone-tab">
                            <div class="input-group">
                                <label class="label" for="phone-input">Enter Phone Number</label>
                                <input type="tel" id="phone-input" class="form-input" placeholder="+****************">
                                <p class="text-sm text-gray-500 mt-1">Generate QR codes that allow users to call you
                                    directly</p>
                            </div>
                        </div>

                        <div class="tab-content" id="email-tab">
                            <div class="input-group">
                                <label class="label" for="email-input">Enter Email Address</label>
                                <input type="email" id="email-input" class="form-input"
                                    placeholder="<EMAIL>">
                                <p class="text-sm text-gray-500 mt-1">Create QR codes that open email composition with
                                    your address</p>
                            </div>
                        </div>
                    </div>

                    <!-- Style Settings -->
                    <div class="card">
                        <h2 class="text-2xl card-title mb-4 text-gray-800">
                            <i class="fas fa-palette mr-2"></i>Style Settings
                        </h2>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <!-- Error Correction Level -->
                            <div class="input-group">
                                <label class="label tooltip" for="error-level"
                                    data-tooltip="Higher error correction allows QR codes to be readable even when damaged, but increases density">
                                    Error Correction Level <i class="fas fa-question-circle text-gray-400"></i>
                                </label>
                                <select id="error-level" class="form-input">
                                    <option value="L">L - Low (~7%)</option>
                                    <option value="M" selected>M - Medium (~15%)</option>
                                    <option value="Q">Q - Quartile (~25%)</option>
                                    <option value="H">H - High (~30%)</option>
                                </select>
                            </div>

                            <!-- Size -->
                            <div class="input-group">
                                <label class="label" for="qr-size">Size: <span id="size-value" class="value-display">300</span>px</label>
                                <input type="range" id="qr-size" class="range-slider" min="200" max="800" value="300"
                                    step="50">
                            </div>

                            <!-- Foreground Color -->
                            <div class="input-group">
                                <label class="label">Foreground Color</label>
                                <div class="flex items-center gap-2">
                                    <input type="color" id="foreground-color" class="color-picker" value="#000000">
                                    <input type="text" id="foreground-hex" class="form-input flex-1" value="#000000"
                                        placeholder="#000000">
                                </div>
                            </div>

                            <!-- Background Color -->
                            <div class="input-group">
                                <label class="label">Background Color</label>
                                <div class="flex items-center gap-2">
                                    <input type="color" id="background-color" class="color-picker" value="#ffffff">
                                    <input type="text" id="background-hex" class="form-input flex-1" value="#ffffff"
                                        placeholder="#ffffff">
                                </div>
                            </div>

                            <!-- Dot Style -->
                            <div class="input-group">
                                <label class="label" for="dot-style">Dot Style</label>
                                <select id="dot-style" class="form-input">
                                    <option value="square">Square</option>
                                    <option value="rounded" selected>Rounded</option>
                                    <option value="dots">Dots</option>
                                    <option value="classy">Classy</option>
                                    <option value="classy-rounded">Classy Rounded</option>
                                </select>
                            </div>

                            <!-- Corner Style -->
                            <div class="input-group">
                                <label class="label" for="corner-style">Corner Style</label>
                                <select id="corner-style" class="form-input">
                                    <option value="square">Square</option>
                                    <option value="extra-rounded" selected>Rounded</option>
                                    <option value="dot">Dot</option>
                                </select>
                            </div>
                        </div>

                        <!-- Advanced Settings -->
                        <div class="mt-6">
                            <h3 class="text-lg font-semibold mb-3 text-gray-700">Advanced Settings</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <!-- Margin -->
                                <div class="input-group">
                                    <label class="label" for="margin">Margin: <span
                                            id="margin-value" class="value-display">10</span>px</label>
                                    <input type="range" id="margin" class="range-slider" min="0" max="50" value="10"
                                        step="5">
                                </div>

                                <!-- Gradient Effect -->
                                <div class="input-group">
                                    <label class="label">
                                        <input type="checkbox" id="enable-gradient" class="mr-2">
                                        Enable Gradient Effect
                                    </label>
                                </div>

                                <!-- Gradient Color Settings -->
                                <div id="gradient-settings" class="col-span-full" style="display: none;">
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                        <div class="input-group">
                                            <label class="label">Gradient Start Color</label>
                                            <input type="color" id="gradient-start" class="color-picker"
                                                value="#667eea">
                                        </div>
                                        <div class="input-group">
                                            <label class="label">Gradient End Color</label>
                                            <input type="color" id="gradient-end" class="color-picker" value="#764ba2">
                                        </div>
                                        <div class="input-group">
                                            <label class="label" for="gradient-direction">Gradient Direction</label>
                                            <select id="gradient-direction" class="form-input">
                                                <option value="0">Horizontal</option>
                                                <option value="90">Vertical</option>
                                                <option value="45" selected>Diagonal</option>
                                                <option value="135">Anti-diagonal</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Logo Settings -->
                    <div class="card">
                        <h2 class="text-2xl card-title mb-4 text-gray-800">
                            <i class="fas fa-image mr-2"></i>Logo Settings
                        </h2>
                        <p class="text-gray-600 mb-4">Add a logo to make your QR code more branded and professional</p>

                        <div class="input-group">
                            <label class="label">
                                <input type="checkbox" id="enable-logo" class="mr-2">
                                Enable Logo
                            </label>
                        </div>

                        <div id="logo-settings" class="space-y-4" style="display: none;">
                            <!-- Logo Source Selection -->
                            <div class="input-group">
                                <label class="label">Logo Source</label>
                                <div class="grid grid-cols-2 gap-2">
                                    <button class="tab-button active" data-logo-tab="upload">
                                        <i class="fas fa-upload mr-1"></i>Upload
                                    </button>
                                    <button class="tab-button" data-logo-tab="default">
                                        <i class="fas fa-star mr-1"></i>Icons
                                    </button>
                                </div>
                            </div>

                            <!-- Upload Logo -->
                            <div class="logo-tab-content active" id="upload-logo-tab">
                                <div class="logo-upload-area" id="logo-upload-area">
                                    <i class="fas fa-cloud-upload-alt text-3xl text-gray-400 mb-2"></i>
                                    <p class="text-gray-600">Click or drag to upload logo image</p>
                                    <p class="text-sm text-gray-400">Supports PNG, JPG, SVG formats</p>
                                    <input type="file" id="logo-file" accept="image/*" style="display: none;">
                                </div>
                            </div>

                            <!-- 默认Logo -->
                            <div class="logo-tab-content" id="default-logo-tab">
                                <div class="grid grid-cols-4 gap-2">
                                    <button class="default-logo-btn" data-logo="heart">
                                        <i class="fas fa-heart text-red-500 text-2xl"></i>
                                    </button>
                                    <button class="default-logo-btn" data-logo="star">
                                        <i class="fas fa-star text-yellow-500 text-2xl"></i>
                                    </button>
                                    <button class="default-logo-btn" data-logo="home">
                                        <i class="fas fa-home text-blue-500 text-2xl"></i>
                                    </button>
                                    <button class="default-logo-btn" data-logo="phone">
                                        <i class="fas fa-phone text-green-500 text-2xl"></i>
                                    </button>
                                    <button class="default-logo-btn" data-logo="email">
                                        <i class="fas fa-envelope text-purple-500 text-2xl"></i>
                                    </button>
                                    <button class="default-logo-btn" data-logo="location">
                                        <i class="fas fa-map-marker-alt text-red-600 text-2xl"></i>
                                    </button>
                                    <button class="default-logo-btn" data-logo="wifi">
                                        <i class="fas fa-wifi text-blue-600 text-2xl"></i>
                                    </button>
                                    <button class="default-logo-btn" data-logo="shopping">
                                        <i class="fas fa-shopping-cart text-orange-500 text-2xl"></i>
                                    </button>
                                </div>
                            </div>

                            <div id="logo-preview-container" style="display: none;">
                                <img id="logo-preview" class="logo-preview" alt="Logo Preview">
                                <button id="remove-logo" class="btn btn-secondary mt-2">
                                    <i class="fas fa-trash mr-1"></i>Remove Logo
                                </button>
                            </div>

                            <!-- Logo Settings -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <!-- Logo Size -->
                                <div class="input-group">
                                    <label class="label" for="logo-size">Logo Size: <span
                                            id="logo-size-value" class="value-display">0.3</span></label>
                                    <input type="range" id="logo-size" class="range-slider" min="0.1" max="0.5"
                                        value="0.3" step="0.05">
                                </div>

                                <!-- Logo Margin -->
                                <div class="input-group">
                                    <label class="label" for="logo-margin">Logo Margin: <span
                                            id="logo-margin-value" class="value-display">10</span>px</label>
                                    <input type="range" id="logo-margin" class="range-slider" min="0" max="30"
                                        value="10" step="2">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Panel - Preview and Download -->
                <div class="space-y-6">
                    <!-- QR Code Preview -->
                    <div class="card fade-in">
                        <div class="flex items-center justify-between mb-4">
                            <h2 class="text-2xl card-title text-gray-800">
                                <i class="fas fa-eye mr-2"></i>Live Preview
                            </h2>
                            <div id="qr-status" class="text-sm status-text text-gray-500">
                                <i class="fas fa-circle text-gray-400 mr-1"></i>Waiting for input
                            </div>
                        </div>
                        <div class="qr-preview bg-gray-50 rounded-lg" id="qr-preview">
                            <div class="text-center text-gray-500">
                                <i class="fas fa-qrcode text-6xl mb-4 pulse-animation"></i>
                                <p>Enter content to generate QR code</p>
                            </div>
                        </div>

                        <!-- QR Code Information -->
                        <div id="qr-info" class="mt-4 p-3 bg-blue-50 rounded-lg" style="display: none;">
                            <h3 class="font-semibold text-gray-700 mb-2">QR Code Details</h3>
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span class="font-medium text-gray-600">Data Length:</span>
                                    <span id="data-length" class="text-blue-600">0</span>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-600">Version:</span>
                                    <span id="qr-version" class="text-blue-600">-</span>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-600">Module Count:</span>
                                    <span id="module-count" class="text-blue-600">-</span>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-600">Error Level:</span>
                                    <span id="current-error-level" class="text-blue-600">M</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Download Options -->
                    <div class="card">
                        <h2 class="text-2xl card-title mb-4 text-gray-800">
                            <i class="fas fa-download mr-2"></i>Download QR Code
                        </h2>

                        <!-- Download Size Setting -->
                        <div class="input-group mb-4">
                            <label class="label" for="download-size">Download Size: <span
                                    id="download-size-value" class="value-display">1024</span>px</label>
                            <input type="range" id="download-size" class="range-slider" min="512" max="2048"
                                value="1024" step="256">
                            <p class="text-sm text-gray-500 mt-1">Higher resolution for better print quality</p>
                        </div>

                        <!-- Download Buttons -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <button id="download-png" class="btn btn-primary" disabled>
                                <i class="fas fa-file-image mr-2"></i>Download PNG
                            </button>
                            <button id="download-svg" class="btn btn-secondary" disabled>
                                <i class="fas fa-file-code mr-2"></i>Download SVG
                            </button>
                        </div>

                        <!-- Batch Download -->
                        <div class="border-t pt-4">
                            <h3 class="text-lg font-semibold mb-3 text-gray-700">Batch Download</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <button id="download-all-sizes" class="btn btn-secondary" disabled>
                                    <i class="fas fa-layer-group mr-2"></i>Multi-Size Pack
                                </button>
                                <button id="download-both-formats" class="btn btn-secondary" disabled>
                                    <i class="fas fa-archive mr-2"></i>PNG+SVG Pack
                                </button>
                            </div>
                        </div>

                        <div class="mt-4 text-sm text-gray-600 space-y-1">
                            <p><i class="fas fa-info-circle mr-1 text-blue-500"></i>PNG format is perfect for printing
                                and sharing</p>
                            <p><i class="fas fa-info-circle mr-1 text-blue-500"></i>SVG format supports lossless scaling
                            </p>
                            <p><i class="fas fa-info-circle mr-1 text-blue-500"></i>Batch downloads include multiple
                                files</p>
                            <p><i class="fas fa-keyboard mr-1 text-green-500"></i>Press Ctrl+S for quick PNG download
                            </p>
                        </div>
                    </div>
                </div>
            </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto text-center">
                <h2 class="text-3xl md:text-4xl feature-title text-gray-800 mb-6">About Our skygamedrop</h2>
                <p class="text-xl text-gray-600 mb-8">
                    We've created the most comprehensive and user-friendly skygamedrop available online.
                    Our tool combines powerful features with an intuitive interface to help you create professional QR
                    codes in seconds.
                </p>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-rocket text-2xl text-blue-600"></i>
                        </div>
                        <h3 class="text-xl feature-title mb-2">Fast & Efficient</h3>
                        <p class="text-gray-600">Generate QR codes instantly with real-time preview. No waiting, no
                            delays.</p>
                    </div>

                    <div class="text-center">
                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-lock text-2xl text-green-600"></i>
                        </div>
                        <h3 class="text-xl feature-title mb-2">Privacy Focused</h3>
                        <p class="text-gray-600">All processing happens in your browser. Your data never leaves your
                            device.</p>
                    </div>

                    <div class="text-center">
                        <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-cogs text-2xl text-purple-600"></i>
                        </div>
                        <h3 class="text-xl feature-title mb-2">Highly Customizable</h3>
                        <p class="text-gray-600">Extensive customization options including colors, logos, and styles.
                        </p>
                    </div>
                </div>

                <div class="bg-gray-50 rounded-lg p-8">
                    <h3 class="text-2xl font-semibold mb-4">Why Choose Our skygamedrop?</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-left">
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">Professional Quality</h4>
                            <p class="text-gray-600">Generate high-resolution QR codes suitable for both digital and
                                print use.</p>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">Multiple Content Types</h4>
                            <p class="text-gray-600">Support for text, URLs, phone numbers, emails, and more content
                                types.</p>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">Brand Integration</h4>
                            <p class="text-gray-600">Add your logo and customize colors to match your brand identity.
                            </p>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">Free Forever</h4>
                            <p class="text-gray-600">No hidden fees, no registration required. Use all features
                                completely free.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-12">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center space-x-4 mb-4">
                        <img src="./images/logo.png" alt="skygamedrop" class="h-8 w-auto">

                    </div>
                    <p class="text-gray-300 mb-4">
                        The most comprehensive and user-friendly skygamedrop.com Create professional,
                        customizable QR codes for any purpose - completely free and privacy-focused.
                    </p>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="index.html#home" class="text-gray-300 hover:text-white transition-colors">Home</a></li>
                        <li><a href="about,html#about" class="text-gray-300 hover:text-white transition-colors">About</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-4">Legal</h3>
                    <ul class="space-y-2">
                        <li><a href="Privacy.html" class="text-gray-300 hover:text-white transition-colors">Privacy
                                Policy</a></li>
                        <li><a href="Terms.html" class="text-gray-300 hover:text-white transition-colors">Terms of
                                Use</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-700 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0">
                    <p>&copy; 2025 skygamedrop.com All rights reserved.</p>
                </div>
                <div class="text-sm text-gray-400">
                    <p>Built with ❤️ using modern web technologies</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-btn').addEventListener('click', function () {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                    // Close mobile menu if open
                    document.getElementById('mobile-menu').classList.add('hidden');
                }
            });
        });

        // 防止移动端水平滚动的额外措施
        function preventHorizontalScroll() {
            // 检查是否有元素超出视口宽度
            const checkOverflow = () => {
                const body = document.body;
                const html = document.documentElement;

                // 如果页面宽度超出视口，强制重置
                if (body.scrollWidth > window.innerWidth || html.scrollWidth > window.innerWidth) {
                    // 查找可能导致溢出的元素
                    const allElements = document.querySelectorAll('*');
                    allElements.forEach(el => {
                        const rect = el.getBoundingClientRect();
                        if (rect.right > window.innerWidth) {
                            // 对超出的元素应用修复
                            el.style.maxWidth = '100%';
                            el.style.overflowX = 'hidden';
                            el.style.wordWrap = 'break-word';
                        }
                    });
                }
            };

            // 页面加载时检查
            checkOverflow();

            // 窗口大小改变时检查
            window.addEventListener('resize', checkOverflow);

            // 防止触摸滑动导致的水平滚动
            let startX = 0;
            document.addEventListener('touchstart', (e) => {
                startX = e.touches[0].clientX;
            }, { passive: true });

            document.addEventListener('touchmove', (e) => {
                const currentX = e.touches[0].clientX;
                const diffX = Math.abs(currentX - startX);
                const diffY = Math.abs(e.touches[0].clientY - (e.touches[0].startY || 0));

                // 如果主要是水平滑动且不在可滚动元素内，阻止默认行为
                if (diffX > diffY && diffX > 10) {
                    const target = e.target;
                    const isScrollable = target.closest('.overflow-x-auto, .overflow-auto, input, textarea, select');
                    if (!isScrollable) {
                        e.preventDefault();
                    }
                }
            }, { passive: false });
        }

        // 在DOM加载完成后执行
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', preventHorizontalScroll);
        } else {
            preventHorizontalScroll();
        }
    </script>
</body>

</html>